import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localization/flutter_localization.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/localization/app_localization.dart';
import 'shared/services/navigation_service.dart';
import 'shared/providers/locale_provider.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  runApp(
    const ProviderScope(
      child: SecurityGuardApp(),
    ),
  );
}

class SecurityGuardApp extends ConsumerStatefulWidget {
  const SecurityGuardApp({super.key});

  @override
  ConsumerState<SecurityGuardApp> createState() => _SecurityGuardAppState();
}

class _SecurityGuardAppState extends ConsumerState<SecurityGuardApp> {
  final FlutterLocalization localization = FlutterLocalization.instance;

  @override
  void initState() {
    configureLocalization();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // Localization
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        ...localization.localizationsDelegates,
      ],
      supportedLocales: localization.supportedLocales,
      locale: localization.currentLocale,

      // Theme
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,

      // Routing
      routerConfig: NavigationService.router,
    );
  }

  void configureLocalization() {
    localization.init(
      mapLocales: AppLocale.locales,
      initLanguageCode: 'sl', // Default to Slovenian
    );
    localization.onTranslatedLanguage = _onTranslatedLanguage;
  }

  void _onTranslatedLanguage(Locale? locale) {
    setState(() {});
  }
}
