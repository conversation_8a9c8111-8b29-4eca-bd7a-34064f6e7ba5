import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/user_model.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../widgets/user_creation_dialog.dart';
import '../widgets/user_details_dialog.dart';

class UsersPage extends ConsumerStatefulWidget {
  const UsersPage({super.key});

  @override
  ConsumerState<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends ConsumerState<UsersPage> {
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: AppLocale.userManagement.getString(context),
      currentIndex: 2, // Guards/Users index
      actions: [
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: () => _showCreateUserDialog(context),
          tooltip: AppLocale.createUser.getString(context),
        ),
      ],
      child: _buildUsersContent(context),
    );
  }

  Widget _buildUsersContent(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1024;
    final isTablet = screenSize.width > 768 && screenSize.width <= 1024;

    double padding = isDesktop ? 32.0 : (isTablet ? 24.0 : 16.0);

    return SingleChildScrollView(
      padding: EdgeInsets.all(padding),
      child: Container(
        constraints: isDesktop ? const BoxConstraints(maxWidth: 1200) : null,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with search and filters
            _buildHeader(context),
            
            const SizedBox(height: 24),
            
            // Users list
            _buildUsersList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocale.users.getString(context),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Search and filter row
            Row(
              children: [
                // Search field
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: AppLocale.searchUsers.getString(context),
                      prefixIcon: const Icon(Icons.search),
                      border: const OutlineInputBorder(),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Role filter dropdown
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedFilter,
                    decoration: InputDecoration(
                      labelText: AppLocale.filterByRole.getString(context),
                      border: const OutlineInputBorder(),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: 'all',
                        child: Text(AppLocale.allRoles.getString(context)),
                      ),
                      DropdownMenuItem(
                        value: 'guard',
                        child: Text(AppLocale.guard.getString(context)),
                      ),
                      DropdownMenuItem(
                        value: 'admin',
                        child: Text(AppLocale.admin.getString(context)),
                      ),
                      DropdownMenuItem(
                        value: 'superadmin',
                        child: Text(AppLocale.superAdmin.getString(context)),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedFilter = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList(BuildContext context) {
    return StreamBuilder<List<UserModel>>(
      stream: _selectedFilter == 'all'
          ? ref.read(userServiceProvider).getUsers()
          : ref.read(userServiceProvider).getUsersByRole(_selectedFilter),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading users: ${snapshot.error}',
                    style: TextStyle(color: AppTheme.errorColor),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => setState(() {}),
                    child: Text(AppLocale.retry.getString(context)),
                  ),
                ],
              ),
            ),
          );
        }

        List<UserModel> users = snapshot.data ?? [];

        // Apply search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          users = users.where((user) {
            return user.firstName.toLowerCase().contains(query) ||
                   user.lastName.toLowerCase().contains(query) ||
                   user.email.toLowerCase().contains(query) ||
                   user.fullName.toLowerCase().contains(query);
          }).toList();
        }

        if (users.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: Column(
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocale.noUsersFound.getString(context),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _searchQuery.isNotEmpty
                        ? 'Try adjusting your search criteria'
                        : 'Create your first user to get started',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton.icon(
                    onPressed: () => _showCreateUserDialog(context),
                    icon: const Icon(Icons.add),
                    label: Text(AppLocale.createUser.getString(context)),
                  ),
                ],
              ),
            ),
          );
        }

        return _buildUsersGrid(context, users);
      },
    );
  }

  Widget _buildUsersGrid(BuildContext context, List<UserModel> users) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1024;
    final isTablet = screenSize.width > 768 && screenSize.width <= 1024;

    int crossAxisCount = isDesktop ? 3 : (isTablet ? 2 : 1);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: isDesktop ? 1.2 : (isTablet ? 1.1 : 1.5),
      ),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildUserCard(context, user);
      },
    );
  }

  Widget _buildUserCard(BuildContext context, UserModel user) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _showUserDetailsDialog(context, user),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with avatar and status
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getRoleColor(user.role).withOpacity(0.1),
                    child: Icon(
                      _getRoleIcon(user.role),
                      color: _getRoleColor(user.role),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.fullName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          user.email,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  // Status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: user.isActive ? AppTheme.successColor : AppTheme.errorColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      user.isActive 
                          ? AppLocale.active.getString(context)
                          : AppLocale.inactive.getString(context),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Role and phone
              Row(
                children: [
                  Icon(
                    Icons.badge,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getRoleDisplayName(context, user.role),
                    style: TextStyle(
                      color: _getRoleColor(user.role),
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              Row(
                children: [
                  Icon(
                    Icons.phone,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      user.phone,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              
              const Spacer(),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _showEditUserDialog(context, user),
                    tooltip: AppLocale.editUser.getString(context),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: () => _showDeleteUserDialog(context, user),
                    tooltip: AppLocale.deleteUser.getString(context),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  Color _getRoleColor(String role) {
    switch (role) {
      case 'superadmin':
        return AppTheme.errorColor;
      case 'admin':
        return AppTheme.primaryColor;
      case 'guard':
        return AppTheme.successColor;
      default:
        return Colors.grey;
    }
  }

  IconData _getRoleIcon(String role) {
    switch (role) {
      case 'superadmin':
        return Icons.admin_panel_settings;
      case 'admin':
        return Icons.manage_accounts;
      case 'guard':
        return Icons.security;
      default:
        return Icons.person;
    }
  }

  String _getRoleDisplayName(BuildContext context, String role) {
    switch (role) {
      case 'superadmin':
        return AppLocale.superAdmin.getString(context);
      case 'admin':
        return AppLocale.admin.getString(context);
      case 'guard':
        return AppLocale.guard.getString(context);
      default:
        return role;
    }
  }

  // Dialog methods
  Future<void> _showCreateUserDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const UserCreationDialog(),
    );

    if (result == true) {
      // User was created successfully, list will update automatically
      debugPrint('User created successfully');
    }
  }

  Future<void> _showEditUserDialog(BuildContext context, UserModel user) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => UserCreationDialog(user: user),
    );

    if (result == true) {
      // User was updated successfully
      debugPrint('User updated successfully');
    }
  }

  Future<void> _showUserDetailsDialog(BuildContext context, UserModel user) async {
    await showDialog(
      context: context,
      builder: (context) => UserDetailsDialog(user: user),
    );
  }

  Future<void> _showDeleteUserDialog(BuildContext context, UserModel user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocale.confirmDeleteUser.getString(context)),
        content: Text(
          AppLocale.deleteUserMessage.getString(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocale.cancel.getString(context)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: Text(
              AppLocale.deleteUser.getString(context),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(userServiceProvider).deleteUser(user.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocale.userDeletedSuccess.getString(context)),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting user: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }
}
