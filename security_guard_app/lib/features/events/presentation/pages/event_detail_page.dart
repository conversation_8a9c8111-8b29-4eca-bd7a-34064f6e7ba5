import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/models/event_model.dart';
import '../../../../shared/models/user_model.dart';


class EventDetailPage extends ConsumerStatefulWidget {
  final String eventId;

  const EventDetailPage({
    super.key,
    required this.eventId,
  });

  @override
  ConsumerState<EventDetailPage> createState() => _EventDetailPageState();
}

class _EventDetailPageState extends ConsumerState<EventDetailPage> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<EventModel?>(
      future: ref.read(eventServiceProvider).getEventById(widget.eventId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return MainLayout(
            title: AppLocale.eventDetails.getString(context),
            currentIndex: 1,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return MainLayout(
            title: AppLocale.eventDetails.getString(context),
            currentIndex: 1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocale.eventNotFound.getString(context),
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.go('/events'),
                    child: Text(AppLocale.backToEvents.getString(context)),
                  ),
                ],
              ),
            ),
          );
        }

        final event = snapshot.data!;
        return MainLayout(
          title: event.title,
          currentIndex: 1,
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showEditDialog(context, event),
              tooltip: AppLocale.editEvent.getString(context),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteDialog(context, event),
              tooltip: AppLocale.deleteEvent.getString(context),
            ),
          ],
          child: _buildEventDetail(context, event),
        );
      },
    );
  }

  Widget _buildEventDetail(BuildContext context, EventModel event) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1024;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => context.go('/events'),
              ),
              Text(
                AppLocale.backToEvents.getString(context),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (isDesktop)
            _buildDesktopLayout(context, event)
          else
            _buildMobileLayout(context, event),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context, EventModel event) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Event Info
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildEventInfoCard(context, event),
              const SizedBox(height: 16),
              _buildLocationCard(context, event),
            ],
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Right Column - Assignment & Status
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildStatusCard(context, event),
              const SizedBox(height: 16),
              _buildAssignmentCard(context, event),
              const SizedBox(height: 16),
              _buildFinancialCard(context, event),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context, EventModel event) {
    return Column(
      children: [
        _buildEventInfoCard(context, event),
        const SizedBox(height: 16),
        _buildStatusCard(context, event),
        const SizedBox(height: 16),
        _buildLocationCard(context, event),
        const SizedBox(height: 16),
        _buildAssignmentCard(context, event),
        const SizedBox(height: 16),
        _buildFinancialCard(context, event),
      ],
    );
  }

  Widget _buildEventInfoCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.event,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    event.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            if (event.description?.isNotEmpty == true) ...[
              const SizedBox(height: 16),
              Text(
                AppLocale.description.getString(context),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                event.description!,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Date and Time
            _buildInfoRow(
              context,
              icon: Icons.access_time,
              title: AppLocale.startTime.getString(context),
              value: DateFormat('EEEE, dd MMMM yyyy • HH:mm').format(event.startDateTime),
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              context,
              icon: Icons.schedule,
              title: AppLocale.duration.getString(context),
              value: '${(event.expectedDurationMinutes / 60).toStringAsFixed(1)} ${AppLocale.hours.getString(context)}',
            ),
            
            if (event.endDateTime != null) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                context,
                icon: Icons.event_available,
                title: AppLocale.endTime.getString(context),
                value: DateFormat('EEEE, dd MMMM yyyy • HH:mm').format(event.endDateTime!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusCard(BuildContext context, EventModel event) {
    final statusColor = _getStatusColor(event.status);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.status.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: statusColor.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    event.status.toUpperCase(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick Actions
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildStatusButton(
                  context,
                  'Confirm',
                  Icons.check_circle,
                  AppTheme.successColor,
                  () => _updateStatus(event, 'confirmed'),
                ),
                _buildStatusButton(
                  context,
                  'Complete',
                  Icons.task_alt,
                  AppTheme.primaryColor,
                  () => _updateStatus(event, 'completed'),
                ),
                _buildStatusButton(
                  context,
                  'Cancel',
                  Icons.cancel,
                  AppTheme.errorColor,
                  () => _updateStatus(event, 'cancelled'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildLocationCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocale.location.getString(context),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Text(
              event.location,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 16),

            // Map placeholder
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.map,
                      size: 32,
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Map integration coming soon',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocale.assignedGuards.getString(context),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (event.assignedGuardIds.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.warningColor.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber,
                      color: AppTheme.warningColor,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        AppLocale.noGuardsAssigned.getString(context),
                        style: TextStyle(
                          color: AppTheme.warningColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              ...event.assignedGuardIds.map((guardId) =>
                _buildGuardItem(context, event, guardId)),

            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showAssignGuardsDialog(context, event),
                icon: const Icon(Icons.person_add),
                label: Text(AppLocale.assignGuards.getString(context)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuardItem(BuildContext context, EventModel event, String guardId) {
    return FutureBuilder<UserModel?>(
      future: ref.read(userServiceProvider).getUserById(guardId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final guard = snapshot.data!;
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).dividerColor,
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                child: Icon(
                  Icons.person,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      guard.fullName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      guard.email,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.remove_circle_outline,
                  color: AppTheme.errorColor,
                ),
                onPressed: () => _removeGuard(event, guardId),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinancialCard(BuildContext context, EventModel event) {
    final totalCost = (event.hourlyRate ?? 0) * (event.expectedDurationMinutes / 60);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.euro,
                  color: AppTheme.successColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocale.financial.getString(context),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildFinancialRow(
              context,
              AppLocale.hourlyRate.getString(context),
              '€${(event.hourlyRate ?? 0).toStringAsFixed(2)}/hr',
            ),

            const SizedBox(height: 12),

            _buildFinancialRow(
              context,
              AppLocale.duration.getString(context),
              '${(event.expectedDurationMinutes / 60).toStringAsFixed(1)} hrs',
            ),

            const SizedBox(height: 12),

            const Divider(),

            const SizedBox(height: 12),

            _buildFinancialRow(
              context,
              AppLocale.totalCost.getString(context),
              '€${totalCost.toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: isTotal ? 18 : 16,
            color: isTotal ? AppTheme.successColor : null,
          ),
        ),
      ],
    );
  }

  // Helper Methods
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'confirmed':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme.warningColor;
      case 'completed':
        return AppTheme.primaryColor;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return Colors.grey;
    }
  }

  void _showEditDialog(BuildContext context, EventModel event) {
    // TODO: Implement event edit dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocale.comingSoon.getString(context)),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, EventModel event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocale.deleteEvent.getString(context)),
        content: Text(AppLocale.deleteEventConfirmation.getString(context)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.cancel.getString(context)),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(eventServiceProvider).deleteEvent(event.id);
              if (context.mounted) {
                context.go('/events');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: Text(AppLocale.delete.getString(context)),
          ),
        ],
      ),
    );
  }

  void _updateStatus(EventModel event, String newStatus) async {
    final updatedEvent = event.copyWith(status: newStatus);
    await ref.read(eventServiceProvider).updateEvent(updatedEvent);
  }

  void _removeGuard(EventModel event, String guardId) async {
    final updatedGuardIds = List<String>.from(event.assignedGuardIds)
      ..remove(guardId);
    final updatedEvent = event.copyWith(assignedGuardIds: updatedGuardIds);
    await ref.read(eventServiceProvider).updateEvent(updatedEvent);
  }

  void _showAssignGuardsDialog(BuildContext context, EventModel event) {
    // TODO: Implement guard assignment dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocale.comingSoon.getString(context)),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
