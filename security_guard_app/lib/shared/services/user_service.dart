import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../models/user_model.dart';
import '../../core/constants/app_constants.dart';

// Provider for UserService
final userServiceProvider = Provider<UserService>((ref) {
  return UserService();
});

class UserService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get all users
  Stream<List<UserModel>> getUsers() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();
    });
  }

  // Get users by role
  Stream<List<UserModel>> getUsersByRole(String role) {
    return _firestore
        .collection(AppConstants.usersCollection)
        .where('role', isEqualTo: role)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();
    });
  }

  // Get users by company
  Stream<List<UserModel>> getUsersByCompany(String companyId) {
    return _firestore
        .collection(AppConstants.usersCollection)
        .where('companyId', isEqualTo: companyId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();
    });
  }

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();
      
      if (doc.exists) {
        final data = doc.data()!;
        return UserModel.fromJson({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user by ID: $e');
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }

  // Create user
  Future<UserModel> createUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phone,
    required String role,
    String? companyId,
  }) async {
    try {
      // Create Firebase Auth user
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final uid = userCredential.user!.uid;

      // Create user document in Firestore
      final userData = {
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
        'phone': phone,
        'role': role,
        'companyId': companyId,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(uid)
          .set(userData);

      // Return the created user
      final createdUser = UserModel(
        id: uid,
        email: email,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        role: role,
        companyId: companyId,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      return createdUser;
    } catch (e) {
      debugPrint('Error creating user: $e');
      throw Exception('Failed to create user: ${e.toString()}');
    }
  }

  // Update user
  Future<void> updateUser(UserModel user) async {
    try {
      final updateData = {
        'firstName': user.firstName,
        'lastName': user.lastName,
        'phone': user.phone,
        'role': user.role,
        'companyId': user.companyId,
        'isActive': user.isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .update(updateData);
    } catch (e) {
      debugPrint('Error updating user: $e');
      throw Exception('Failed to update user: ${e.toString()}');
    }
  }

  // Delete user
  Future<void> deleteUser(String userId) async {
    try {
      // Delete from Firestore
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .delete();

      // Note: We don't delete from Firebase Auth as it requires the user to be signed in
      // In a production app, you might want to use Firebase Admin SDK for this
    } catch (e) {
      debugPrint('Error deleting user: $e');
      throw Exception('Failed to delete user: ${e.toString()}');
    }
  }

  // Toggle user active status
  Future<void> toggleUserStatus(String userId, bool isActive) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': isActive,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error toggling user status: $e');
      throw Exception('Failed to update user status: ${e.toString()}');
    }
  }

  // Search users by name or email
  Future<List<UserModel>> searchUsers(String query) async {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a simple implementation that gets all users and filters locally
      // For production, consider using Algolia or similar service
      
      final snapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .get();

      final users = snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();

      final lowercaseQuery = query.toLowerCase();
      
      return users.where((user) {
        return user.firstName.toLowerCase().contains(lowercaseQuery) ||
               user.lastName.toLowerCase().contains(lowercaseQuery) ||
               user.email.toLowerCase().contains(lowercaseQuery) ||
               user.fullName.toLowerCase().contains(lowercaseQuery);
      }).toList();
    } catch (e) {
      debugPrint('Error searching users: $e');
      throw Exception('Failed to search users: ${e.toString()}');
    }
  }

  // Get guards only
  Stream<List<UserModel>> getGuards() {
    return getUsersByRole(AppConstants.roleGuard);
  }

  // Get admins only
  Stream<List<UserModel>> getAdmins() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .where('role', whereIn: [AppConstants.roleAdmin, AppConstants.roleSuperAdmin])
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();
    });
  }

  // Get user statistics
  Future<Map<String, int>> getUserStatistics() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .get();

      final users = snapshot.docs.map((doc) {
        final data = doc.data();
        return UserModel.fromJson({...data, 'id': doc.id});
      }).toList();

      return {
        'total': users.length,
        'active': users.where((u) => u.isActive).length,
        'inactive': users.where((u) => !u.isActive).length,
        'guards': users.where((u) => u.role == AppConstants.roleGuard).length,
        'admins': users.where((u) => u.role == AppConstants.roleAdmin || u.role == AppConstants.roleSuperAdmin).length,
      };
    } catch (e) {
      debugPrint('Error getting user statistics: $e');
      throw Exception('Failed to get user statistics: ${e.toString()}');
    }
  }
}
