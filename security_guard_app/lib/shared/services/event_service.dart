import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/event_model.dart';

// Provider for EventService
final eventServiceProvider = Provider<EventService>((ref) {
  return EventService();
});

class EventService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all events
  Stream<List<EventModel>> getEvents() {
    return _firestore
        .collection('events')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          return EventModel.fromJson(data);
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    }).handleError((error) {
      debugPrint('Error in getEvents stream: $error');
      throw Exception('Failed to load events: ${error.toString()}');
    });
  }

  // Get events for a specific guard
  Stream<List<EventModel>> getEventsForGuard(String guardId) {
    return _firestore
        .collection('events')
        .where('assignedGuardIds', arrayContains: guardId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          return EventModel.fromJson(data);
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    }).handleError((error) {
      debugPrint('Error in getEventsForGuard stream: $error');
      throw Exception('Failed to load guard events: ${error.toString()}');
    });
  }

  // Get events by status
  Stream<List<EventModel>> getEventsByStatus(String status) {
    return _firestore
        .collection('events')
        .where('status', isEqualTo: status)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          data['id'] = doc.id;
          return EventModel.fromJson(data);
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    }).handleError((error) {
      debugPrint('Error in getEventsByStatus stream: $error');
      throw Exception('Failed to load events by status: ${error.toString()}');
    });
  }

  // Create a new event
  Future<String> createEvent(EventModel event) async {
    try {
      final eventData = event.toJson();
      eventData.remove('id'); // Remove id for creation
      eventData['createdAt'] = FieldValue.serverTimestamp();
      eventData['updatedAt'] = FieldValue.serverTimestamp();

      final docRef = await _firestore.collection('events').add(eventData);
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating event: $e');
      throw Exception('Failed to create event: ${e.toString()}');
    }
  }

  // Update an event
  Future<void> updateEvent(EventModel event) async {
    try {
      final eventData = event.toJson();
      eventData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection('events').doc(event.id).update(eventData);
    } catch (e) {
      debugPrint('Error updating event: $e');
      throw Exception('Failed to update event: ${e.toString()}');
    }
  }

  // Delete an event
  Future<void> deleteEvent(String eventId) async {
    try {
      await _firestore.collection('events').doc(eventId).delete();
    } catch (e) {
      debugPrint('Error deleting event: $e');
      throw Exception('Failed to delete event: ${e.toString()}');
    }
  }

  // Assign guard to event
  Future<void> assignGuardToEvent(String eventId, String guardId) async {
    try {
      await _firestore.collection('events').doc(eventId).update({
        'assignedGuardIds': FieldValue.arrayUnion([guardId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error assigning guard to event: $e');
      throw Exception('Failed to assign guard: ${e.toString()}');
    }
  }

  // Remove guard from event
  Future<void> removeGuardFromEvent(String eventId, String guardId) async {
    try {
      await _firestore.collection('events').doc(eventId).update({
        'assignedGuardIds': FieldValue.arrayRemove([guardId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error removing guard from event: $e');
      throw Exception('Failed to remove guard: ${e.toString()}');
    }
  }

  // Update event status
  Future<void> updateEventStatus(String eventId, String status) async {
    try {
      await _firestore.collection('events').doc(eventId).update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating event status: $e');
      throw Exception('Failed to update event status: ${e.toString()}');
    }
  }

  // Get event by ID
  Future<EventModel?> getEventById(String eventId) async {
    try {
      final doc = await _firestore.collection('events').doc(eventId).get();
      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;
        return EventModel.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting event: $e');
      throw Exception('Failed to get event: ${e.toString()}');
    }
  }
}
