import 'package:flutter_localization/flutter_localization.dart';

class AppLocale {
  static const String title = 'title';
  static const String appTitle = 'appTitle';
  
  // Navigation
  static const String dashboard = 'dashboard';
  static const String events = 'events';
  static const String guards = 'guards';
  static const String companies = 'companies';
  static const String reports = 'reports';
  static const String settings = 'settings';
  static const String profile = 'profile';
  
  // User
  static const String adminPanel = 'adminPanel';
  static const String administrator = 'administrator';
  static const String signOut = 'signOut';
  static const String login = 'login';
  static const String logout = 'logout';
  static const String email = 'email';
  static const String password = 'password';
  static const String forgotPassword = 'forgotPassword';
  
  // Dashboard
  static const String welcomeBack = 'welcomeBack';
  static const String todaysOverview = 'todaysOverview';
  static const String todaysEvents = 'todaysEvents';
  static const String hoursWorked = 'hoursWorked';
  static const String pending = 'pending';
  static const String completed = 'completed';
  static const String quickActions = 'quickActions';
  static const String clockIn = 'clockIn';
  static const String clockOut = 'clockOut';
  
  // Events
  static const String eventsManagement = 'eventsManagement';
  static const String all = 'all';
  static const String active = 'active';
  static const String confirmed = 'confirmed';
  static const String cancelled = 'cancelled';
  static const String errorLoadingEvents = 'errorLoadingEvents';
  static const String noEventsFound = 'noEventsFound';
  static const String createFirstEvent = 'createFirstEvent';
  static const String noFilteredEvents = 'noFilteredEvents';
  static const String createEvent = 'createEvent';
  static const String createEventDialog = 'createEventDialog';
  static const String eventDetailsFor = 'eventDetailsFor';
  static const String location = 'location';
  static const String duration = 'duration';
  static const String hourlyRate = 'hourlyRate';
  static const String guardsAssigned = 'guardsAssigned';
  
  // Common actions
  static const String accept = 'accept';
  static const String decline = 'decline';
  static const String save = 'save';
  static const String cancel = 'cancel';
  static const String delete = 'delete';
  static const String edit = 'edit';
  static const String add = 'add';
  static const String create = 'create';
  static const String update = 'update';
  static const String filter = 'filter';
  static const String loading = 'loading';
  static const String error = 'error';
  static const String success = 'success';
  static const String noData = 'noData';
  static const String retry = 'retry';
  
  // Event templates
  static const String importEvents = 'importEvents';
  static const String exportEvents = 'exportEvents';
  static const String eventTemplates = 'eventTemplates';
  static const String eventsOverview = 'eventsOverview';
  static const String totalEvents = 'totalEvents';

  // Event creation dialog
  static const String createEventTitle = 'createEventTitle';
  static const String editEventTitle = 'editEventTitle';
  static const String eventTitle = 'eventTitle';
  static const String eventDescription = 'eventDescription';
  static const String startDate = 'startDate';
  static const String startTime = 'startTime';
  static const String endDate = 'endDate';
  static const String endTime = 'endTime';
  static const String selectDate = 'selectDate';
  static const String selectTime = 'selectTime';
  static const String assignGuards = 'assignGuards';
  static const String selectGuards = 'selectGuards';
  static const String noGuardsSelected = 'noGuardsSelected';
  static const String guardsSelected = 'guardsSelected';
  static const String eventTitleRequired = 'eventTitleRequired';
  static const String eventDescriptionRequired = 'eventDescriptionRequired';
  static const String eventLocationRequired = 'eventLocationRequired';
  static const String startDateRequired = 'startDateRequired';
  static const String endDateRequired = 'endDateRequired';
  static const String endDateAfterStart = 'endDateAfterStart';
  static const String eventCreatedSuccess = 'eventCreatedSuccess';
  static const String eventUpdatedSuccess = 'eventUpdatedSuccess';
  static const String eventCreationFailed = 'eventCreationFailed';

  // User management
  static const String userManagement = 'userManagement';
  static const String users = 'users';
  static const String createUser = 'createUser';
  static const String editUser = 'editUser';
  static const String deleteUser = 'deleteUser';
  static const String firstName = 'firstName';
  static const String lastName = 'lastName';
  static const String phone = 'phone';
  static const String role = 'role';
  static const String company = 'company';
  static const String status = 'status';
  static const String inactive = 'inactive';
  static const String superAdmin = 'superAdmin';
  static const String admin = 'admin';
  static const String guard = 'guard';
  static const String selectRole = 'selectRole';
  static const String userCreatedSuccess = 'userCreatedSuccess';
  static const String userUpdatedSuccess = 'userUpdatedSuccess';
  static const String userDeletedSuccess = 'userDeletedSuccess';
  static const String userCreationFailed = 'userCreationFailed';
  static const String confirmDeleteUser = 'confirmDeleteUser';
  static const String deleteUserMessage = 'deleteUserMessage';
  static const String noUsersFound = 'noUsersFound';
  static const String searchUsers = 'searchUsers';
  static const String filterByRole = 'filterByRole';
  static const String allRoles = 'allRoles';
  static const String firstNameRequired = 'firstNameRequired';
  static const String lastNameRequired = 'lastNameRequired';
  static const String emailRequired = 'emailRequired';
  static const String phoneRequired = 'phoneRequired';
  static const String roleRequired = 'roleRequired';
  static const String invalidEmail = 'invalidEmail';
  static const String invalidPhone = 'invalidPhone';
  static const String userDetails = 'userDetails';
  static const String lastLogin = 'lastLogin';
  static const String createdDate = 'createdDate';
  static const String permissions = 'permissions';
  static const String canManageUsers = 'canManageUsers';
  static const String canCreateEvents = 'canCreateEvents';
  static const String canViewReports = 'canViewReports';

  // Additional strings
  static const String optional = 'optional';
  static const String endDateOptionalHint = 'endDateOptionalHint';

  // Reports and Analytics
  static const String reportsAnalytics = 'reportsAnalytics';
  static const String exportReport = 'exportReport';
  static const String reportFilters = 'reportFilters';
  static const String reportType = 'reportType';
  static const String overviewReport = 'overviewReport';
  static const String financialReport = 'financialReport';
  static const String attendanceReport = 'attendanceReport';
  static const String guardPerformance = 'guardPerformance';
  static const String dateRange = 'dateRange';
  static const String refresh = 'refresh';
  static const String completedEvents = 'completedEvents';
  static const String totalHours = 'totalHours';
  static const String totalRevenue = 'totalRevenue';
  static const String quickStats = 'quickStats';
  static const String comingSoon = 'comingSoon';

  // Event Details
  static const String eventDetails = 'eventDetails';
  static const String eventNotFound = 'eventNotFound';
  static const String backToEvents = 'backToEvents';
  static const String editEvent = 'editEvent';
  static const String deleteEvent = 'deleteEvent';
  static const String deleteEventConfirmation = 'deleteEventConfirmation';
  static const String hours = 'hours';
  static const String assignedGuards = 'assignedGuards';
  static const String noGuardsAssigned = 'noGuardsAssigned';
  static const String financial = 'financial';
  static const String totalCost = 'totalCost';
  static const String description = 'description';

  static const List<MapLocale> locales = [
    MapLocale(
      'en',
      {
        title: 'Security Guard Management',
        appTitle: 'Security Guard Management',
        
        // Navigation
        dashboard: 'Dashboard',
        events: 'Events',
        guards: 'Guards',
        companies: 'Companies',
        reports: 'Reports',
        settings: 'Settings',
        profile: 'Profile',
        
        // User
        adminPanel: 'Admin Panel',
        administrator: 'Administrator',
        signOut: 'Sign Out',
        login: 'Login',
        logout: 'Logout',
        email: 'Email',
        password: 'Password',
        forgotPassword: 'Forgot Password?',
        
        // Dashboard
        welcomeBack: 'Welcome Back!',
        todaysOverview: 'Here\'s your overview for today',
        todaysEvents: 'Today\'s Events',
        hoursWorked: 'Hours Worked',
        pending: 'Pending',
        completed: 'Completed',
        quickActions: 'Quick Actions',
        clockIn: 'Clock In',
        clockOut: 'Clock Out',
        
        // Events
        eventsManagement: 'Events Management',
        all: 'All',
        active: 'Active',
        confirmed: 'Confirmed',
        cancelled: 'Cancelled',
        errorLoadingEvents: 'Error loading events',
        noEventsFound: 'No events found',
        createFirstEvent: 'Create your first event to get started',
        noFilteredEvents: 'No {status} events found',
        createEvent: 'Create Event',
        createEventDialog: 'Create event dialog coming soon...',
        eventDetailsFor: 'Event details for: {title}',
        location: 'Location',
        duration: 'Duration',
        hourlyRate: 'Hourly Rate',
        guardsAssigned: '{count} guard(s) assigned',
        
        // Common actions
        accept: 'Accept',
        decline: 'Decline',
        save: 'Save',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        add: 'Add',
        create: 'Create',
        update: 'Update',
        filter: 'Filter',
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        noData: 'No Data',
        retry: 'Retry',
        
        // Event templates
        importEvents: 'Import Events',
        exportEvents: 'Export Events',
        eventTemplates: 'Event Templates',
        eventsOverview: 'Events Overview',
        totalEvents: 'Total Events',

        // Event creation dialog
        createEventTitle: 'Create New Event',
        editEventTitle: 'Edit Event',
        eventTitle: 'Event Title',
        eventDescription: 'Description',
        startDate: 'Start Date',
        startTime: 'Start Time',
        endDate: 'End Date',
        endTime: 'End Time',
        selectDate: 'Select Date',
        selectTime: 'Select Time',
        assignGuards: 'Assign Guards',
        selectGuards: 'Select Guards',
        noGuardsSelected: 'No guards selected',
        guardsSelected: '{count} guard(s) selected',
        eventTitleRequired: 'Event title is required',
        eventDescriptionRequired: 'Description is required',
        eventLocationRequired: 'Location is required',
        startDateRequired: 'Start date is required',
        endDateRequired: 'End date is required',
        endDateAfterStart: 'End date must be after start date',
        eventCreatedSuccess: 'Event created successfully',
        eventUpdatedSuccess: 'Event updated successfully',
        eventCreationFailed: 'Failed to create event',

        // User management
        userManagement: 'User Management',
        users: 'Users',
        createUser: 'Create User',
        editUser: 'Edit User',
        deleteUser: 'Delete User',
        firstName: 'First Name',
        lastName: 'Last Name',
        phone: 'Phone',
        role: 'Role',
        company: 'Company',
        status: 'Status',
        inactive: 'Inactive',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        guard: 'Guard',
        selectRole: 'Select Role',
        userCreatedSuccess: 'User created successfully',
        userUpdatedSuccess: 'User updated successfully',
        userDeletedSuccess: 'User deleted successfully',
        userCreationFailed: 'Failed to create user',
        confirmDeleteUser: 'Confirm Delete',
        deleteUserMessage: 'Are you sure you want to delete this user? This action cannot be undone.',
        noUsersFound: 'No users found',
        searchUsers: 'Search users...',
        filterByRole: 'Filter by role',
        allRoles: 'All Roles',
        firstNameRequired: 'First name is required',
        lastNameRequired: 'Last name is required',
        emailRequired: 'Email is required',
        phoneRequired: 'Phone number is required',
        roleRequired: 'Role is required',
        invalidEmail: 'Please enter a valid email address',
        invalidPhone: 'Please enter a valid phone number',
        userDetails: 'User Details',
        lastLogin: 'Last Login',
        createdDate: 'Created Date',
        permissions: 'Permissions',
        canManageUsers: 'Can manage users',
        canCreateEvents: 'Can create events',
        canViewReports: 'Can view reports',

        // Additional strings
        optional: 'optional',
        endDateOptionalHint: 'If not specified, defaults to same day 1 hour after start time',

        // Reports and Analytics
        reportsAnalytics: 'Reports & Analytics',
        exportReport: 'Export Report',
        reportFilters: 'Report Filters',
        reportType: 'Report Type',
        overviewReport: 'Overview Report',
        financialReport: 'Financial Report',
        attendanceReport: 'Attendance Report',
        guardPerformance: 'Guard Performance',
        dateRange: 'Date Range',
        refresh: 'Refresh',
        completedEvents: 'Completed Events',
        totalHours: 'Total Hours',
        totalRevenue: 'Total Revenue',
        quickStats: 'Quick Statistics',
        comingSoon: 'Coming Soon',

        // Event Details
        eventDetails: 'Event Details',
        eventNotFound: 'Event not found',
        backToEvents: 'Back to Events',
        editEvent: 'Edit Event',
        deleteEvent: 'Delete Event',
        deleteEventConfirmation: 'Are you sure you want to delete this event? This action cannot be undone.',
        hours: 'hours',
        assignedGuards: 'Assigned Guards',
        noGuardsAssigned: 'No guards assigned to this event',
        financial: 'Financial Information',
        totalCost: 'Total Cost',
        description: 'Description',
      },
    ),
    MapLocale(
      'sl',
      {
        title: 'Upravljanje Varnostnikov',
        appTitle: 'Upravljanje Varnostnikov',
        
        // Navigation
        dashboard: 'Nadzorna plošča',
        events: 'Dogodki',
        guards: 'Varnostniki',
        companies: 'Podjetja',
        reports: 'Poročila',
        settings: 'Nastavitve',
        profile: 'Profil',
        
        // User
        adminPanel: 'Skrbniška plošča',
        administrator: 'Administrator',
        signOut: 'Odjavi se',
        login: 'Prijava',
        logout: 'Odjava',
        email: 'E-pošta',
        password: 'Geslo',
        forgotPassword: 'Pozabljeno geslo?',
        
        // Dashboard
        welcomeBack: 'Dobrodošli nazaj!',
        todaysOverview: 'Tukaj je vaš pregled za danes',
        todaysEvents: 'Današnji dogodki',
        hoursWorked: 'Opravljene ure',
        pending: 'V obravnavi',
        completed: 'Končano',
        quickActions: 'Hitre akcije',
        clockIn: 'Prijava na delo',
        clockOut: 'Odjava z dela',
        
        // Events
        eventsManagement: 'Upravljanje dogodkov',
        all: 'Vse',
        active: 'Aktivno',
        confirmed: 'Potrjeno',
        cancelled: 'Preklicano',
        errorLoadingEvents: 'Napaka pri nalaganju dogodkov',
        noEventsFound: 'Ni najdenih dogodkov',
        createFirstEvent: 'Ustvarite svoj prvi dogodek za začetek',
        noFilteredEvents: 'Ni najdenih {status} dogodkov',
        createEvent: 'Ustvari dogodek',
        createEventDialog: 'Dialog za ustvarjanje dogodka kmalu na voljo...',
        eventDetailsFor: 'Podrobnosti dogodka za: {title}',
        location: 'Lokacija',
        duration: 'Trajanje',
        hourlyRate: 'Urna postavka',
        guardsAssigned: '{count} varnostnik(ov)',
        
        // Common actions
        accept: 'Sprejmi',
        decline: 'Zavrni',
        save: 'Shrani',
        cancel: 'Prekliči',
        delete: 'Izbriši',
        edit: 'Uredi',
        add: 'Dodaj',
        create: 'Ustvari',
        update: 'Posodobi',
        filter: 'Filter',
        loading: 'Nalaganje...',
        error: 'Napaka',
        success: 'Uspeh',
        noData: 'Ni podatkov',
        retry: 'Poskusi znova',
        
        // Event templates
        importEvents: 'Uvozi dogodke',
        exportEvents: 'Izvozi dogodke',
        eventTemplates: 'Predloge dogodkov',
        eventsOverview: 'Pregled dogodkov',
        totalEvents: 'Skupaj dogodkov',

        // Event creation dialog
        createEventTitle: 'Ustvari nov dogodek',
        editEventTitle: 'Uredi dogodek',
        eventTitle: 'Naslov dogodka',
        eventDescription: 'Opis',
        startDate: 'Datum začetka',
        startTime: 'Čas začetka',
        endDate: 'Datum konca',
        endTime: 'Čas konca',
        selectDate: 'Izberi datum',
        selectTime: 'Izberi čas',
        assignGuards: 'Dodeli varnostnike',
        selectGuards: 'Izberi varnostnike',
        noGuardsSelected: 'Ni izbranih varnostnikov',
        guardsSelected: '{count} varnostnik(ov) izbranih',
        eventTitleRequired: 'Naslov dogodka je obvezen',
        eventDescriptionRequired: 'Opis je obvezen',
        eventLocationRequired: 'Lokacija je obvezna',
        startDateRequired: 'Datum začetka je obvezen',
        endDateRequired: 'Datum konca je obvezen',
        endDateAfterStart: 'Datum konca mora biti po datumu začetka',
        eventCreatedSuccess: 'Dogodek je bil uspešno ustvarjen',
        eventUpdatedSuccess: 'Dogodek je bil uspešno posodobljen',
        eventCreationFailed: 'Ustvarjanje dogodka ni uspelo',

        // User management
        userManagement: 'Upravljanje uporabnikov',
        users: 'Uporabniki',
        createUser: 'Ustvari uporabnika',
        editUser: 'Uredi uporabnika',
        deleteUser: 'Izbriši uporabnika',
        firstName: 'Ime',
        lastName: 'Priimek',
        phone: 'Telefon',
        role: 'Vloga',
        company: 'Podjetje',
        status: 'Status',
        inactive: 'Neaktiven',
        superAdmin: 'Super administrator',
        admin: 'Administrator',
        guard: 'Varnostnik',
        selectRole: 'Izberi vlogo',
        userCreatedSuccess: 'Uporabnik je bil uspešno ustvarjen',
        userUpdatedSuccess: 'Uporabnik je bil uspešno posodobljen',
        userDeletedSuccess: 'Uporabnik je bil uspešno izbrisan',
        userCreationFailed: 'Ustvarjanje uporabnika ni uspelo',
        confirmDeleteUser: 'Potrdi brisanje',
        deleteUserMessage: 'Ali ste prepričani, da želite izbrisati tega uporabnika? Tega dejanja ni mogoče razveljaviti.',
        noUsersFound: 'Ni najdenih uporabnikov',
        searchUsers: 'Išči uporabnike...',
        filterByRole: 'Filtriraj po vlogi',
        allRoles: 'Vse vloge',
        firstNameRequired: 'Ime je obvezno',
        lastNameRequired: 'Priimek je obvezen',
        emailRequired: 'E-pošta je obvezna',
        phoneRequired: 'Telefonska številka je obvezna',
        roleRequired: 'Vloga je obvezna',
        invalidEmail: 'Vnesite veljaven e-poštni naslov',
        invalidPhone: 'Vnesite veljavno telefonsko številko',
        userDetails: 'Podrobnosti uporabnika',
        lastLogin: 'Zadnja prijava',
        createdDate: 'Datum ustvarjanja',
        permissions: 'Dovoljenja',
        canManageUsers: 'Lahko upravlja uporabnike',
        canCreateEvents: 'Lahko ustvarja dogodke',
        canViewReports: 'Lahko pregleduje poročila',

        // Additional strings
        optional: 'neobvezno',
        endDateOptionalHint: 'Če ni določeno, se privzeto nastavi na isti dan 1 uro po času začetka',

        // Reports and Analytics
        reportsAnalytics: 'Poročila in analitika',
        exportReport: 'Izvozi poročilo',
        reportFilters: 'Filtri poročil',
        reportType: 'Vrsta poročila',
        overviewReport: 'Pregled poročila',
        financialReport: 'Finančno poročilo',
        attendanceReport: 'Poročilo o prisotnosti',
        guardPerformance: 'Uspešnost varnostnikov',
        dateRange: 'Časovno obdobje',
        refresh: 'Osveži',
        completedEvents: 'Končani dogodki',
        totalHours: 'Skupaj ur',
        totalRevenue: 'Skupni prihodek',
        quickStats: 'Hitre statistike',
        comingSoon: 'Kmalu na voljo',

        // Event Details
        eventDetails: 'Podrobnosti dogodka',
        eventNotFound: 'Dogodek ni najden',
        backToEvents: 'Nazaj na dogodke',
        editEvent: 'Uredi dogodek',
        deleteEvent: 'Izbriši dogodek',
        deleteEventConfirmation: 'Ali ste prepričani, da želite izbrisati ta dogodek? Tega dejanja ni mogoče razveljaviti.',
        hours: 'ur',
        assignedGuards: 'Dodeljeni varnostniki',
        noGuardsAssigned: 'Temu dogodku ni dodeljen noben varnostnik',
        financial: 'Finančne informacije',
        totalCost: 'Skupni strošek',
        description: 'Opis',
      },
    ),
  ];
}
